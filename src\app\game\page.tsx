"use client";

import { useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";

let socket: Socket | null = null;

export default function RoomChat() {
  const [roomId, setRoomId] = useState("");
  const [joinedRoom, setJoinedRoom] = useState<string | null>(null);
  const [messages, setMessages] = useState<string[]>([]);
  const [input, setInput] = useState("");

  useEffect(() => {
    if (!socket) {
      socket = io("http://localhost:4000"); // point to your socket server
    }

    socket.on("room-message", (data: { sender: string; message: string }) => {
      setMessages((prev) => [...prev, `${data.sender}: ${data.message}`]);
    });

    return () => {
      socket?.off("room-message");
    };
  }, []);

  const joinRoom = () => {
    if (!roomId.trim()) return;
    socket?.emit("join-room", roomId);
    setJoinedRoom(roomId);
    setMessages([]);
  };

  const sendMessage = () => {
    if (!input.trim() || !joinedRoom) return;
    socket?.emit("room-message", { roomId: joinedRoom, message: input });
    setInput("");
  };

  return (
    <div className="p-4 max-w-md mx-auto">
      {!joinedRoom ? (
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Enter room ID"
            value={roomId}
            onChange={(e) => setRoomId(e.target.value)}
            className="border p-2 flex-1 rounded"
          />
          <button onClick={joinRoom} className="bg-blue-500 text-white px-4 py-2 rounded">
            Join
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <h2 className="font-bold">Room: {joinedRoom}</h2>

          <div className="border rounded p-2 h-40 overflow-y-auto bg-gray-50">
            {messages.length > 0 ? messages.map((msg, i) => <p key={i}>{msg}</p>) : <p className="text-gray-400">No messages yet...</p>}
          </div>

          <div className="flex gap-2">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Type a message"
              className="border p-2 flex-1 rounded"
            />
            <button onClick={sendMessage} className="bg-green-500 text-white px-4 py-2 rounded">
              Send
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
