import { createServer } from "http";
import { Server } from "socket.io";

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: "*", // replace with your Next.js URL in production
  },
});

io.on("connection", (socket) => {
  console.log("✅ User connected:", socket.id);

  // Join a room
  socket.on("join-room", (roomId: string) => {
    socket.join(roomId);
    console.log(`${socket.id} joined room ${roomId}`);

    // Notify just this client
    socket.emit("room-message", { sender: "system", message: `Joined room ${roomId}` });

    // Notify others in the room
    socket.to(roomId).emit("room-message", {
      sender: "system",
      message: `${socket.id} has joined`,
    });
  });

  // Leave a room
  socket.on("leave-room", (roomId: string) => {
    socket.leave(roomId);
    console.log(`${socket.id} left room ${roomId}`);

    // Notify this client
    socket.emit("room-message", { sender: "system", message: `Left room ${roomId}` });

    // Notify others
    socket.to(roomId).emit("room-message", {
      sender: "system",
      message: `${socket.id} has left`,
    });
  });

  // Send a message to a room
  socket.on("room-message", ({ roomId, message }: { roomId: string; message: string }) => {
    io.to(roomId).emit("room-message", { sender: socket.id, message });
  });

  // Handle disconnect
  socket.on("disconnect", () => {
    console.log("❌ User disconnected:", socket.id);
  });
});

httpServer.listen(4000, () => {
  console.log("🚀 Socket.IO server running on http://localhost:4000");
});
